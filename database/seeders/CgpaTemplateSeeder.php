<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\CgpaTemplateExport;

class CgpaTemplateSeeder extends Seeder
{
    public function run()
    {
        $path = public_path('templates/cgpa_import_template.xlsx');
        
        // Ensure the directory exists
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }
        
        // Generate the Excel file
        Excel::store(new CgpaTemplateExport(), 'templates/cgpa_import_template.xlsx', 'public');
        
        $this->command->info('CGPA template has been generated at: ' . $path);
    }
}
