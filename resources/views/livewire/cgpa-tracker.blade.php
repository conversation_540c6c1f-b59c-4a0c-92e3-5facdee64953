 @extends('layouts.app')

@section('main')

<div class="min-h-screen bg-gray-100 dark:bg-gray-900">
    <!-- Success/Error <PERSON> -->
    <div x-data="{ show: true }" x-show="show" x-init="setTimeout(() => show = false, 5000)">
        @if (session('status'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('status') }}</span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3" @click="show = false">
                    <svg class="fill-current h-6 w-6 text-green-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <title>Close</title>
                        <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                    </svg>
                </span>
            </div>
        @endif
        
        @if (session('error'))
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
                <span class="block sm:inline">{{ session('error') }}</span>
                <span class="absolute top-0 bottom-0 right-0 px-4 py-3" @click="show = false">
                    <svg class="fill-current h-6 w-6 text-red-500" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                        <title>Close</title>
                        <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                    </svg>
                </span>
            </div>
        @endif
    </div>

    <div class="p-6">
        <div class="flex justify-between items-center mb-6">
            <h1 class="text-2xl font-bold text-gray-800 dark:text-gray-200">Student CGPA Tracker</h1>
            <button 
                wire:click="create"
                class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm"
                @click="$dispatch('show-edit-modal', true)"
            >
                <i class="fas fa-plus mr-1"></i> Add New Student
            </button>
        </div>
    
        <!-- Stats Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
            <div class="p-4 bg-blue-100 dark:bg-blue-900 rounded-lg shadow">
                <div class="text-sm text-blue-700 dark:text-blue-200">Total Students</div>
                <div class="text-3xl font-bold text-blue-900 dark:text-blue-100">{{ $total }}</div>
            </div>
            <div class="p-4 bg-red-100 dark:bg-red-900 rounded-lg shadow">
                <div class="text-sm text-red-700 dark:text-red-200">At-Risk (&lt; {{ number_format($threshold,1) }})</div>
                <div class="text-3xl font-bold text-red-900 dark:text-red-100">{{ $atRisk }}</div>
            </div>
            <div class="p-4 bg-green-100 dark:bg-green-900 rounded-lg shadow">
                <div class="text-sm text-green-700 dark:text-green-200">Risk %</div>
                <div class="text-3xl font-bold text-green-900 dark:text-green-100">{{ $total ? round(($atRisk/$total)*100,1) : 0 }}%</div>
            </div>
        </div>
    
        <!-- Import / Export Actions -->
        <div class="flex flex-wrap items-center gap-3 mb-6">
            <button 
                wire:click="showImportModal" 
                @click="$dispatch('show-import-modal', true)" 
                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm"
            >
                <i class="fas fa-upload mr-1"></i> Import Excel
            </button>
            <button wire:click="prepareExport" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg text-sm">
                <i class="fas fa-file-excel mr-1"></i> Export
            </button>
       
        </div>
    
        <!-- Filters -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
            <input type="text" placeholder="Search by Name, Index, Program..." wire:model.debounce.500ms="search" class="border rounded-lg px-3 py-2 text-sm w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white" title="Search by Name, Index Number, Program, or Department">
    
            <input type="text" placeholder="Filter by Program" wire:model.debounce.500ms="filterProgram" class="border rounded-lg px-3 py-2 text-sm w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white">
    
            <input type="text" placeholder="Filter by Semester" wire:model.debounce.500ms="filterSemester" class="border rounded-lg px-3 py-2 text-sm w-full dark:bg-gray-700 dark:border-gray-600 dark:text-white">
    
            <label class="inline-flex items-center text-sm text-gray-700 dark:text-gray-300">
                <input type="checkbox" wire:model.live="belowThresholdOnly" class="rounded mr-2 dark:bg-gray-700 dark:border-gray-600">
                <span>Show CGPA &lt; {{ number_format($threshold, 1) }}</span>
            </label>
        </div>
    
        <!-- Students Table -->
        <div class="overflow-x-auto bg-white dark:bg-gray-900 shadow rounded-lg">
    
        <!-- Import Excel Modal -->
        <div x-data="{ show: false }" 
             x-show="show"
             x-init="
                Livewire.on('show-import-modal', value => { show = value });
                $watch('show', value => {
                    if (value) {
                        document.body.classList.add('overflow-hidden');
                    } else {
                        document.body.classList.remove('overflow-hidden');
                        $wire.set('showImportModal', false, false);
                    }
                });
                if ($wire.entangle('showImportModal')) {
                    show = $wire.entangle('showImportModal');
                }
             "
             x-on:keydown.escape.window="show = false"
             class="fixed inset-0 z-50 overflow-y-auto" 
             x-cloak
             :class="{ 'hidden': !show }">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div x-show="show" 
                     x-transition:enter="ease-out duration-300" 
                     x-transition:enter-start="opacity-0" 
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                     @click="show = false">
                </div>

                <!-- Modal panel -->
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                
                <div x-show="show"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6">
                    
                    <!-- Modal header -->
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-blue-600 dark:text-blue-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                                Import Student CGPA Data
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    Upload an Excel file containing student CGPA records. Ensure your file matches the required format.
                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Download Template</h4>
                                        <a href="{{ asset('templates/cgpa_import_template.xlsx') }}" 
                                           class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                            Download Excel Template
                                        </a>
                                    </div>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- File upload area -->
                    <div class="mt-6">
                        <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 dark:border-gray-600 border-dashed rounded-md">
                            <div class="space-y-1 text-center">
                                <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                                </svg>
                                <div class="flex text-sm text-gray-600 dark:text-gray-300 justify-center">
                                    <label for="file-upload" class="relative cursor-pointer bg-white dark:bg-gray-800 rounded-md font-medium text-blue-600 hover:text-blue-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-blue-500">
                                        <span>Upload a file</span>
                                        <input id="file-upload" wire:model="file" type="file" class="sr-only" accept=".xlsx, .xls, .csv">
                                    </label>
                                    <p class="pl-1">or drag and drop</p>
                                </div>
                                <p class="text-xs text-gray-500 dark:text-gray-400">
                                    XLSX, XLS, or CSV (Max 10MB)
                                </p>
                                @error('file')
                                    <p class="mt-2 text-sm text-red-600 dark:text-red-400">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Instructions -->
                    <div class="mt-4 bg-blue-50 dark:bg-blue-900/30 p-4 rounded-md">
                        <h4 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">File Requirements:</h4>
                        <ul class="text-xs text-blue-700 dark:text-blue-300 space-y-1 list-disc list-inside">
                            <li>Use the template above to ensure proper formatting</li>
                            <li>Required fields: Index Number, Name, GPA, CGPA, Program, Department</li>
                            <li>File size should not exceed 10MB</li>
                            <li>Supported formats: .xlsx, .xls, .csv</li>
                        </ul>
                    </div>

                    <!-- Action buttons -->
                    <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                        <button type="button" 
                                wire:click="importFile"
                                wire:loading.attr="disabled"
                                wire:target="file importFile"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm disabled:opacity-50">
                            <span wire:loading.remove wire:target="importFile">Import</span>
                            <span wire:loading wire:target="importFile">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Importing...
                            </span>
                        </button>
                        <button type="button" 
                                @click="show = false"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        {{-- <!-- Import Path Modal -->
        @if($showImportPathModal)
            <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                    <h2 class="text-xl font-bold mb-4">Import from Path</h2>
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">File Path</label>
                        <input type="text" wire:model="importPath" class="w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300">
                        @error('importPath')
                            <span class="text-red-500 text-sm">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="flex justify-end space-x-2">
                        <button wire:click="showImportPathModal = false" class="px-4 py-2 bg-gray-500 hover:bg-gray-600 text-white rounded-lg">
                            Cancel
                        </button>
                        <button wire:click="importFromPath" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg">
                            Import
                        </button>
                    </div>
                </div>
            </div>
        @endif --}}
            <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead class="bg-gray-50 dark:bg-gray-800">
                    <tr>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Index Number</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">GPA</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">CGPA</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Point</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Program</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Semester</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Department</th>
                        <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider w-40">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    @forelse($students as $student)
                        <tr class="{{ $student->cgpa < $threshold ? 'bg-red-50 dark:bg-red-900/30' : '' }}">
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $student->index_number }}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $student->name }}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ number_format($student->gpa, 2) }}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ number_format($student->cgpa, 2) }}
                                @if($student->cgpa < $threshold)
                                    <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-red-100 text-red-700 dark:bg-red-700 dark:text-red-100">
                                        At Risk
                                    </span>
                                @elseif($student->cgpa >= $threshold && $student->cgpa < ($threshold + 0.5)) {{-- Example: Good but can improve --}}
                                    <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-yellow-100 text-yellow-700 dark:bg-yellow-600 dark:text-yellow-100">
                                        Fair
                                    </span>
                                @else
                                    <span class="ml-2 px-2 py-0.5 rounded-full text-xs font-semibold bg-green-100 text-green-700 dark:bg-green-700 dark:text-green-100">
                                        Good
                                    </span>
                                @endif
                            </td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ number_format($student->totalpoint, 2) }}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $student->program }}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $student->semester }}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ $student->department }}</td>
                            <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 space-x-1">
                                <button wire:click="edit({{ $student->id }})" class="px-2 py-1 bg-yellow-500 text-white rounded hover:bg-yellow-600 text-xs">
                                    <i class="fas fa-edit"></i> Edit
                                </button>
                                <button wire:click="$dispatch('confirm-delete', { id: {{ $student->id }}, name: '{{ $student->name }}' })" class="px-2 py-1 bg-red-600 text-white rounded hover:bg-red-700 text-xs">
                                    Delete
                                </button>
                                <button wire:click="sendAlert({{ $student->id }})" class="px-2 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-xs" title="Send Alert">
                                    <i class="fas fa-bell"></i>
                                </button>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="3" class="px-4 py-4 text-center text-sm text-gray-500 dark:text-gray-400">No records found.</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    
        <div class="mt-4">
            {{ $students->links() }}
        </div>
        <!-- Edit Modal -->
        <div x-data="{ show: false }" 
             x-show="show" 
             x-on:show-edit-modal.window="show = true"
             x-on:hide-edit-modal.window="show = false"
             class="fixed inset-0 overflow-y-auto" style="display: none;">
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="show" 
                     x-transition:enter="ease-out duration-300" 
                     x-transition:enter-start="opacity-0" 
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     class="fixed inset-0 transition-opacity" 
                     @click="show = false">
                    <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>
    
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                
                <div x-show="show"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full sm:p-6">
                    <form wire:submit.prevent="save">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100 mb-4">
                            {{ $editingId ? 'Edit Student CGPA' : 'Add New Student' }}
                        </h3>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <!-- Left Column -->
                            <div class="space-y-4">
                                <!-- Index Number -->
                                <div>
                                    <label for="index_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Index Number *</label>
                                    <input type="text" wire:model="form.index_number" id="index_number" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.index_number') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- Name -->
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Full Name *</label>
                                    <input type="text" wire:model="form.name" id="name" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- Program -->
                                <div>
                                    <label for="program" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Program *</label>
                                    <input type="text" wire:model="form.program" id="program" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.program') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- Department -->
                                <div>
                                    <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Department *</label>
                                    <input type="text" wire:model="form.department" id="department" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.department') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                            </div>
                            
                            <!-- Right Column -->
                            <div class="space-y-4">
                                <!-- Semester -->
                                <div>
                                    <label for="semester" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Semester *</label>
                                    <input type="text" wire:model="form.semester" id="semester" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.semester') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- Year of Enrollment -->
                                <div>
                                    <label for="year_of_enrollment" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year of Enrollment *</label>
                                    <input type="number" wire:model="form.year_of_enrollment" id="year_of_enrollment" min="1900" max="{{ date('Y') + 5 }}"
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.year_of_enrollment') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- GPA -->
                                <div>
                                    <label for="gpa" class="block text-sm font-medium text-gray-700 dark:text-gray-300">GPA (0-4.0) *</label>
                                    <input type="number" step="0.01" min="0" max="4.0" wire:model="form.gpa" id="gpa" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.gpa') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- CGPA -->
                                <div>
                                    <label for="cgpa" class="block text-sm font-medium text-gray-700 dark:text-gray-300">CGPA (0-4.0) *</label>
                                    <input type="number" step="0.01" min="0" max="4.0" wire:model="form.cgpa" id="cgpa" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.cgpa') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- Total Credit -->
                                <div>
                                    <label for="totalcredit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Total Credit *</label>
                                    <input type="number" min="0" wire:model="form.totalcredit" id="totalcredit" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.totalcredit') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- Total Point -->
                                <div>
                                    <label for="totalpoint" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Total Point *</label>
                                    <input type="number" step="0.01" min="0" wire:model="form.totalpoint" id="totalpoint" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.totalpoint') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                                
                                <!-- Email -->
                                <div>
                                    <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                                    <input type="email" wire:model="form.email" id="email" 
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                    @error('form.email') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                            <button type="submit" 
                                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm">
                                {{ $editingId ? 'Update' : 'Create' }}
                            </button>
                            <button type="button" 
                                    @click="show = false"
                                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Import Modal -->
        <div x-data="{ show: false }" 
             x-show="show"
             x-on:show-import-modal.window="show = $event.detail"
             x-on:keydown.escape="show = false"
             class="fixed inset-0 overflow-y-auto" style="display: none;">
            <!-- Add a click handler to close modal when clicking outside -->
            <div x-show="show" @click="show = false" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="show" 
                     x-transition:enter="ease-out duration-300" 
                     x-transition:enter-start="opacity-0" 
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100" 
                     x-transition:leave-end="opacity-0"
                     class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity">
                </div>
                <div x-show="show"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                    <div>
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                            <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-5">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                Import Student CGPA Data
                            </h3>
                            <div class="mt-2">
                                <form wire:submit.prevent="importFile" class="space-y-4">
                                    <div>
                                        <label for="file" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Excel File *</label>
                                        <input type="file" wire:model="file" id="file" 
                                               accept=".xlsx,.xls,.csv" 
                                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                                        @error('file') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                                    </div>
                                    <div class="mt-4 pt-4 border-t border-gray-200">
                                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Download Template</h4>
                                        <a href="{{ route('cgpa.template.download') }}" 
                                           class="inline-flex items-center px-4 py-2 bg-teal-600 hover:bg-teal-700 text-white rounded-lg text-sm"
                                           download="cgpa_import_template.xlsx">
                                            <i class="fas fa-file-download mr-1"></i> Download Excel Template
                                        </a>
                                    </div>
                                    <div class="mt-4">
                                        <button type="submit" 
                                                class="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm" 
                                                wire:loading.attr="disabled" 
                                                wire:target="file">
                                            <i class="fas fa-upload mr-1"></i> Import
                                        </button>
                                        <div wire:loading wire:target="file" class="text-sm text-gray-300">Uploading...</div>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 sm:mt-6">
                        <button type="button" 
                                x-on:click="show = false"
                                class="inline-flex justify-center w-full rounded-md border border-transparent shadow-sm px-4 py-2 bg-gray-600 text-base font-medium text-white hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Delete Confirmation Modal -->
        <div x-data="{ show: false, id: null, name: '' }" 
             x-show="show" 
             x-on:confirm-delete.window="
                show = true;
                id = $event.detail.id;
                name = $event.detail.name;
             "
             x-on:keydown.escape="show = false"
             class="fixed inset-0 overflow-y-auto" style="display: none;">
            <!-- Add a click handler to close modal when clicking outside -->
            <div x-show="show" @click="show = false" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="show" 
                     x-transition:enter="ease-out duration-300" 
                     x-transition:enter-start="opacity-0" 
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     class="fixed inset-0 transition-opacity" 
                     @click="show = false">
                    <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>
    
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
                
                <div x-show="show"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                     x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                     class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                    <div>
                        <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                            <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:mt-5">
                            <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                                Delete Student
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500 dark:text-gray-300">
                                    Are you sure you want to delete <span x-text="name" class="font-semibold"></span>? This action cannot be undone.
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                        <button type="button" 
                                @click="
                                    $wire.delete(id);
                                    show = false;
                                "
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:col-start-2 sm:text-sm">
                            Delete
                        </button>
                        <button type="button" 
                                @click="show = false"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    
        <!-- Export Confirmation Modal -->
        <div x-data="{ show: @entangle('showExportModal') }"
             x-show="show"
             x-on:keydown.escape.window="$wire.set('showExportModal', false)"
             class="fixed inset-0 overflow-y-auto" 
             style="display: none;"
             x-cloak>
            <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <div x-show="show"
                     x-transition:enter="ease-out duration-300"
                     x-transition:enter-start="opacity-0"
                     x-transition:enter-end="opacity-100"
                     x-transition:leave="ease-in duration-200"
                     x-transition:leave-start="opacity-100"
                     x-transition:leave-end="opacity-0"
                     class="fixed inset-0 transition-opacity"
                     @click="show = false">
                    <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
                </div>
    
                <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
    
                <!-- Export confirmation dialog removed -->
        
        @push('scripts')
        <script>
            document.addEventListener('livewire:initialized', () => {
                // Listen for success messages and show toast
                Livewire.on('saved', (event) => { // Changed to event to access detail
                    window.fireToast('success', event.detail || event[0] || 'Saved successfully!'); // Access message from event detail
                });
                
                // Listen for error messages and show toast
                Livewire.on('error', (event) => { // Changed to event to access detail
                    window.fireToast('error', event.detail || event[0] || 'An error occurred.'); // Access message from event detail
                });
            });
        </script>
        </div>
    </div>

    <!-- Edit/Create Student Modal -->
    <div x-data="{ show: @entangle('showEditModal').defer }" 
         x-show="show"
         x-on:keydown.escape.window="show = false"
         class="fixed inset-0 z-50 overflow-y-auto"
         x-cloak>
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <!-- Background overlay -->
            <div x-show="show" 
                 x-transition:enter="ease-out duration-300" 
                 x-transition:enter-start="opacity-0" 
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
                 @click="show = false">
            </div>

            <!-- Modal panel -->
            <span class="hidden sm:inline-block sm:align-middle sm:h-screen">&#8203;</span>
            
            <div x-show="show"
                 x-transition:enter="ease-out duration-300"
                 x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave="ease-in duration-200"
                 x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100"
                 x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                 class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6">
                
                <!-- Modal header -->
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 dark:bg-blue-900 sm:mx-0 sm:h-10 sm:w-10">
                        <i class="fas {{ $editingId ? 'fa-edit' : 'fa-user-plus' }} text-blue-600 dark:text-blue-300"></i>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100">
                            {{ $editingId ? 'Edit Student CGPA' : 'Add New Student' }}
                        </h3>
                    </div>
                </div>

                <!-- Form -->
                <form wire:submit.prevent="save" class="mt-5">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- Left Column -->
                        <div class="space-y-4">
                            <!-- Index Number -->
                            <div>
                                <label for="index_number" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Index Number *</label>
                                <input type="text" id="index_number" wire:model.defer="form.index_number" 
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.index_number') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>

                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Full Name *</label>
                                <input type="text" id="name" wire:model.defer="form.name" 
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.name') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>

                            <!-- Program -->
                            <div>
                                <label for="program" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Program *</label>
                                <input type="text" id="program" wire:model.defer="form.program" 
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.program') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>

                            <!-- Department -->
                            <div>
                                <label for="department" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Department *</label>
                                <input type="text" id="department" wire:model.defer="form.department" 
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.department') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>
                        </div>

                        <!-- Right Column -->
                        <div class="space-y-4">
                            <!-- GPA -->
                            <div>
                                <label for="gpa" class="block text-sm font-medium text-gray-700 dark:text-gray-300">GPA *</label>
                                <input type="number" id="gpa" wire:model.defer="form.gpa" step="0.01" min="0" max="4"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.gpa') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>

                            <!-- CGPA -->
                            <div>
                                <label for="cgpa" class="block text-sm font-medium text-gray-700 dark:text-gray-300">CGPA *</label>
                                <input type="number" id="cgpa" wire:model.defer="form.cgpa" step="0.01" min="0" max="4"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.cgpa') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>

                            <!-- Total Credit -->
                            <div>
                                <label for="totalcredit" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Total Credit *</label>
                                <input type="number" id="totalcredit" wire:model.defer="form.totalcredit" min="0"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.totalcredit') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>

                            <!-- Semester -->
                            <div>
                                <label for="semester" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Semester *</label>
                                <input type="text" id="semester" wire:model.defer="form.semester" 
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.semester') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>

                            <!-- Year of Enrollment -->
                            <div>
                                <label for="year_of_enrollment" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Year of Enrollment *</label>
                                <input type="number" id="year_of_enrollment" wire:model.defer="form.year_of_enrollment" min="1900" max="{{ date('Y') + 5 }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm"
                                       required>
                                @error('form.year_of_enrollment') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Email</label>
                                <input type="email" id="email" wire:model.defer="form.email" 
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white sm:text-sm">
                                @error('form.email') <span class="text-red-500 text-xs">{{ $message }}</span> @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Action buttons -->
                    <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                        <button type="submit" 
                                wire:loading.attr="disabled"
                                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm disabled:opacity-50">
                            <span wire:loading.remove wire:target="save">
                                {{ $editingId ? 'Update' : 'Save' }}
                            </span>
                            <span wire:loading wire:target="save">
                                <i class="fas fa-spinner fa-spin mr-1"></i> Saving...
                            </span>
                        </button>
                        <button type="button" 
                                @click="show = false"
                                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white dark:bg-gray-700 text-base font-medium text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Listen for Livewire events
        document.addEventListener('livewire:initialized', () => {
            @this.on('alert', (data) => {
                // Show toast notification
                const toast = document.createElement('div');
                toast.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg ${data.type === 'error' ? 'bg-red-100 border-l-4 border-red-500 text-red-700' : 'bg-green-100 border-l-4 border-green-500 text-green-700'}`;
                toast.role = 'alert';
                toast.innerHTML = `
                    <div class="flex">
                        <div class="flex-shrink-0">
                            ${data.type === 'error' ? 
                                '<i class="fas fa-exclamation-circle text-red-500"></i>' : 
                                '<i class="fas fa-check-circle text-green-500"></i>'}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">${data.message}</p>
                        </div>
                        <div class="ml-4">
                            <button type="button" class="text-gray-500 hover:text-gray-700" @click="this.parentElement.parentElement.remove()">
                                <span class="sr-only">Close</span>
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `;
                document.body.appendChild(toast);
                
                // Auto-remove after 5 seconds
                setTimeout(() => {
                    toast.remove();
                }, 5000);
            });
        });
    </script>
    @endpush

    @endsection
