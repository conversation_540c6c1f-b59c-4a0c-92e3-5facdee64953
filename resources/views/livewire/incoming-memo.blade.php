@extends('layouts.app')

@section('main')

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        {{-- Header Section --}}
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Incoming Memos Tracker</h1>
                    <p class="text-gray-600 dark:text-gray-300 mt-1">Manage and track all incoming correspondence</p>
                </div>  
                <button wire:click="create" x-data @click="$dispatch('open-modal', 'add-user-modal')" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    New Memo
                </button>
            </div>
        </div>

        {{-- Search and Filter Section --}}
        <div class="bg-white dark:bg-gray-800  rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-white dark:bg-gray-800 mb-4">Search & Filter</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Search</label>
                    <div class="relative">
                        <input type="text" 
                               wire:model.debounce.300ms="search" 
                               placeholder="Search memos..." 
                               class="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors duration-200">
                        <svg class="absolute left-3 top-3.5 h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">From Date</label>
                    <input type="date" 
                           wire:model="filters.date_from" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors duration-200">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">To Date</label>
                    <input type="date" 
                           wire:model="filters.date_to" 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors duration-200">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Sender</label>
                    <input type="text" 
                           wire:model.debounce.300ms="filters.sender" 
                           placeholder="Filter by sender..." 
                           class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white transition-colors duration-200">
                </div>
            </div>
            <div class="mt-4 flex justify-end">
                <button wire:click="resetFilters" 
                        class="inline-flex items-center px-4 py-2 text-sm font-medium text-white hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Reset Filters
                </button>
            </div>
        </div>

        {{-- Table Section --}}
        <div class="bg-dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div class="overflow-x-auto">
                <table class="w-full">
                    <thead class="bg-dark:bg-gray-800border-b border-gray-200">
                        <tr>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Date Received</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Registry No</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">From</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Date of Letter</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Letter No</th>
                            <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Subject</th>
                            {{-- <th class="px-6 py-4 text-left text-xs font-medium text-white uppercase tracking-wider">Remark</th> --}}

                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @forelse ($memos as $incomingMemo)
                            <tr class="hover:bg-gray-50 transition-colors duration-150">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $incomingMemo->date_received->format('M d, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                    {{ $incomingMemo->registry_no }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                                    {{ $incomingMemo->from_whom_received }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $incomingMemo->date_of_letter->format('M d, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                    {{ $incomingMemo->letter_no }}
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-900 max-w-sm truncate">
                                    <span title="{{ $incomingMemo->subject }}">{{ $incomingMemo->subject }}</span>
                                </td>
                                {{-- <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                                    <span title="{{ $incomingMemo->remark }}">{{ $incomingMemo->remark }}</span>
                                </td> --}}
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-3">
                                        <button wire:click="edit({{ $incomingMemo->id }})"
                                                x-data @click="$dispatch('open-modal', 'add-user-modal')"
                                                class="text-blue-600 hover:text-blue-700 hover:bg-blue-50 px-3 py-1 rounded-md transition-colors duration-200">
                                            Edit
                                        </button>
                                        <button wire:click="confirmDelete({{ $incomingMemo->id }})"
                                                x-data @click="$dispatch('open-modal', 'delete-memo-modal')"
                                                class="text-red-600 hover:text-red-700 hover:bg-red-50 px-3 py-1 rounded-md transition-colors duration-200">
                                            Delete
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-6 py-12 text-center">
                                    <div class="text-white">
                                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                        <p class="text-lg font-medium">No memos found</p>
                                        <p class="text-sm">Try adjusting your search criteria</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>

        {{-- Pagination --}}
        <div class="mt-6">
            {{ $memos->links() }}
        </div>
    </div>

    {{-- Modal --}}
    <x-modal name="add-user-modal" focusable :show="$showModal">
        <div class="flex items-center justify-center pt-4 px-4 text-center sm:block sm:p-0">
            <!-- Modal panel -->
            <div class="inline-block align-bottom bg-white  rounded-lg border-t-4 border-blue-600 px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full sm:p-6">
                <div class="sm:flex sm:items-start">
                    <div class="w-full">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-2xl font-bold text-blue-600">{{ $editingId ? 'Edit Memo' : 'Create New Memo' }}</h3>
                            <button @click="show = false; $wire.closeModal()" class="text-gray-400 hover:text-gray-500 focus:outline-none">
                                <svg class="h-6 w-6" stroke="currentColor" fill="none" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <form wire:submit.prevent="save" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">Date Received <span class="text-red-500">*</span></label>
                                    <input type="date" wire:model.defer="date_received" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-dark-900 transition-colors duration-200 @error('date_received') border-red-500 @enderror">
                                    @error('date_received') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">Registry No<span class="text-red-500">*</span></label>
                                    <input type="text" wire:model.defer="registry_no" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-dark-900 transition-colors duration-200 @error('registry_no') border-red-500 @enderror">
                                    @error('registry_no') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>
                                
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">Received From <span class="text-red-500">*</span></label>
                                    <input type="text" wire:model.defer="from_whom_received" placeholder="Enter sender's name" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-dark-900 transition-colors duration-200 @error('from_whom_received') border-red-500 @enderror">
                                    @error('from_whom_received') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">No of letter <span class="text-red-500">*</span></label>
                                    <input type="text" wire:model.defer="letter_no" placeholder="Enter letter number" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-dark-900 transition-colors duration-200 @error('letter_no') border-red-500 @enderror">
                                    @error('letter_no') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-900 mb-2">Date of Letter <span class="text-red-500">*</span></label>
                                    <input type="date" wire:model.defer="date_of_letter" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-dark-900 transition-colors duration-200 @error('date_of_letter') border-red-500 @enderror">
                                    @error('date_of_letter') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Remarks <span class="text-red-500">*</span></label>
                                    <input type="text" wire:model.defer="remark" placeholder="Enter remarks" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500  dark:text-dark-900 transition-colors duration-200 @error('remark') border-red-500 @enderror">
                                    @error('remark') <p class="mt-1 text-sm text-red-600">{{ $message }}</p> @enderror
                                </div>

                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700  mb-2">Subject <span class="text-red-500">*</span></label>
                                    <textarea wire:model.defer="subject" rows="3" placeholder="Enter subject" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:text-dark-900 transition-colors duration-200"></textarea>
                                </div>
                            </div>

                            <div class="flex justify-end space-x-4 pt-4 border-t border-gray-200 ">
                                <button type="button" wire:click="closeModal" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-900 rounded-lg hover:bg-gray-50  focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200">Cancel</button>
                                <button type="submit" class="px-6 py-3 bg-blue-600 text-white font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200">{{ $editingId ? 'Update Memo' : 'Save Memo' }}</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </x-modal>

    {{-- Delete Confirmation Modal --}}
    <x-modal name="delete-memo-modal" focusable>
        <div class="flex items-center justify-center pt-4 px-4 text-center sm:block sm:p-0">
            <div class="inline-block align-bottom bg-white rounded-lg border-t-4 border-red-600 px-4 pt-5 pb-4 text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Delete Memo</h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">Are you sure you want to delete this memo? This action cannot be undone.</p>
                        </div>
                        <div class="mt-6 flex justify-end space-x-4">
                            <button type="button" @click="show = false; $wire.closeModal()" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors duration-200">Cancel</button>
                            <button type="button" wire:click="delete({{ $deletingId ?? 0 }})" @click="show = false" class="px-4 py-2 bg-red-600 text-white font-medium rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200">Delete</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </x-modal>

@endsection
