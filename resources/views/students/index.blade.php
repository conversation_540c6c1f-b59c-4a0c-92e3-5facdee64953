@extends('layouts.app')

@section('main')
<div class="max-w-7xl mx-auto py-8">
    <div class="flex flex-wrap items-center justify-between gap-4 mb-6">
        <h2 class="text-2xl font-semibold text-white">Students</h2>
        <div class="flex flex-wrap items-center gap-3">
            <button onclick="openModal()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Student
            </button>
            <button onclick="document.getElementById('importModal').classList.remove('hidden')" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                </svg>
                Import
            </button>
            <a href="{{ route('students.export') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export
            </a>
        </div>
    </div>

    @if(session('success'))
        <div class="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
            {{ session('success') }}
        </div>
    @endif

    @if(session('error'))
        <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded">
            {{ session('error') }}
        </div>
    @endif

    @if(session('import_errors'))
        <div class="mb-4 p-4 bg-yellow-100 border border-yellow-400 text-yellow-700 rounded">
            <h4 class="font-semibold mb-2">Import completed with some issues:</h4>
            <ul class="list-disc pl-5">
                @foreach(session('import_errors') as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <form id="searchForm" method="get" class="mb-4 flex flex-wrap gap-3 items-end">
        <div>
            <label class="block text-sm font-medium mb-1 text-white">Department</label>
            <select name="department" class="border-gray-300 rounded-lg w-48">
                <option value="">All Departments</option>
                @foreach($departments as $dept)
                    <option value="{{ $dept }}" {{ request('department') == $dept ? 'selected' : '' }}>{{ $dept }}</option>
                @endforeach
            </select>
        </div>
        <div>
            <label class="block text-sm font-medium mb-1 text-white">Program</label>
            <input type="text" name="program" value="{{ request('program') }}" class="border-gray-300 rounded-lg" placeholder="Program...">
        </div>
        <div>
            <label class="block text-sm font-medium mb-1 text-white">Year</label>
            <input type="number" name="year" value="{{ request('year') }}" class="border-gray-300 rounded-lg" placeholder="YYYY" min="2000">
        </div>
        <div>
            <label class="block text-sm font-medium mb-1 text-white">Search</label>
            <input type="text" name="q" value="{{ request('q') }}" class="border-gray-300 rounded-lg" placeholder="Name / Index...">
        </div>
        <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">Filter</button>
        @if(request()->hasAny(['q', 'department', 'program', 'year']))
            <a href="{{ route('students.index') }}" class="px-4 py-2 bg-gray-200 rounded-lg hover:bg-gray-300">Clear Filters</a>
        @endif
    </form>

    @if($students->count())
        <div class="overflow-x-auto bg-white shadow rounded-lg">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Department</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Program</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Year</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Index</th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($students as $stu)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $stu->name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $stu->department }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $stu->program }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $stu->year_of_enrollment }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ $stu->index_number }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-right">
                                <div class="flex items-center justify-end space-x-2">
                                    <button 
                                        onclick="openEditModal({{ $stu->id }}, '{{ $stu->name }}', '{{ $stu->department }}', '{{ $stu->program }}', {{ $stu->year_of_enrollment }}, '{{ $stu->index_number }}')" 
                                        class="p-1.5 text-blue-600 hover:bg-blue-50 rounded-full transition-colors duration-200"
                                        title="Edit"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                    </button>
                                    <button 
                                        onclick="openDeleteModal({{ $stu->id }}, '{{ addslashes($stu->name) }}')" 
                                        class="p-1.5 text-red-600 hover:bg-red-50 rounded-full transition-colors duration-200"
                                        title="Delete"
                                    >
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        
        <!-- Pagination -->
        <div class="mt-6 flex items-center justify-between px-2">
            <div class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ $students->firstItem() }}</span> to 
                <span class="font-medium">{{ $students->lastItem() }}</span> of 
                <span class="font-medium">{{ $students->total() }}</span> students
            </div>
            <div class="flex space-x-2">
                @if ($students->onFirstPage())
                    <span class="px-3 py-1 rounded-md bg-gray-100 text-gray-400 cursor-not-allowed">
                        &laquo; Previous
                    </span>
                @else
                    <a href="{{ $students->previousPageUrl() }}" class="px-3 py-1 rounded-md bg-white border border-gray-300 hover:bg-gray-50">
                        &laquo; Previous
                    </a>
                @endif

                @foreach ($students->getUrlRange(1, $students->lastPage()) as $page => $url)
                    @if ($page == $students->currentPage())
                        <span class="px-3 py-1 rounded-md bg-blue-600 text-white">
                            {{ $page }}
                        </span>
                    @else
                        <a href="{{ $url }}" class="px-3 py-1 rounded-md bg-white border border-gray-300 hover:bg-gray-50">
                            {{ $page }}
                        </a>
                    @endif
                @endforeach

                @if ($students->hasMorePages())
                    <a href="{{ $students->nextPageUrl() }}" class="px-3 py-1 rounded-md bg-white border border-gray-300 hover:bg-gray-50">
                        Next &raquo;
                    </a>
                @else
                    <span class="px-3 py-1 rounded-md bg-gray-100 text-gray-400 cursor-not-allowed">
                        Next &raquo;
                    </span>
                @endif
            </div>
        </div>
    @else
    <div class="flex items-center justify-center h-32 text-gray-500">
        <i data-lucide="user" class="w-5 h-5 mr-2"></i>
        <p class="text-lg">No students found.</p>
    </div>
    
    @endif
</div>

<!-- Add Student Modal -->
<div id="studentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-2xl mx-4 max-h-[90vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-6">
            <h3 id="modalTitle" class="text-xl font-semibold">Add Student</h3>
            <button onclick="closeModal()" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <form id="studentForm" method="POST">
            @csrf
            <input type="hidden" id="methodField" name="_method" value="">
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium mb-1">Name</label>
                    <input type="text" id="name" name="name" class="w-full border-gray-300 rounded-lg" required>
                    <p class="text-red-600 text-sm hidden" id="error-name"></p>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-1">Department</label>
                        <input type="text" id="department" name="department" class="w-full border-gray-300 rounded-lg" required>
                        <p class="text-red-600 text-sm hidden" id="error-department"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Program</label>
                        <input type="text" id="program" name="program" class="w-full border-gray-300 rounded-lg" required>
                        <p class="text-red-600 text-sm hidden" id="error-program"></p>
                    </div>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-1">Year of Enrollment</label>
                        <input type="number" id="year_of_enrollment" name="year_of_enrollment" class="w-full border-gray-300 rounded-lg" min="2000" max="2100" required>
                        <p class="text-red-600 text-sm hidden" id="error-year_of_enrollment"></p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium mb-1">Index Number</label>
                        <input type="text" id="index_number" name="index_number" class="w-full border-gray-300 rounded-lg" required>
                        <p class="text-red-600 text-sm hidden" id="error-index_number"></p>
                    </div>
                </div>
                <div class="flex justify-end gap-3">
                    <button type="button" onclick="closeModal()" class="px-4 py-2 bg-gray-200 rounded-lg">Cancel</button>
                    <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">Save</button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold">Confirm Deletion</h3>
            <button onclick="closeDeleteModal()" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <p class="mb-6">Are you sure you want to delete <span id="studentToDelete" class="font-semibold"></span>?</p>
        
        <div class="flex justify-end gap-3">
            <button type="button" onclick="closeDeleteModal()" class="px-4 py-2 bg-gray-200 rounded-lg">Cancel</button>
            <form id="deleteForm" method="POST" class="inline">
                @csrf
                @method('DELETE')
                <button type="submit" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700">
                    Delete
                </button>
            </form>
        </div>
    </div>
</div>

<!-- Import Modal -->
<div id="importModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold">Import Students</h3>
            <button onclick="document.getElementById('importModal').classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        
        <div class="mb-4">
            <p class="text-sm text-gray-600 mb-4">
                Upload an Excel file (.xlsx, .xls, .csv) with the following columns:<br>
                <span class="text-xs font-mono">student_name, department, program, year_of_enrollment, index_number</span>
            </p>
            
            <form action="{{ route('students.import') }}" method="POST" enctype="multipart/form-data" class="space-y-4">
                @csrf
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Select File</label>
                    <input type="file" name="file" accept=".xlsx,.xls,.csv" required
                           class="block w-full text-sm text-gray-500
                                  file:mr-4 file:py-2 file:px-4
                                  file:rounded-md file:border-0
                                  file:text-sm file:font-semibold
                                  file:bg-blue-50 file:text-blue-700
                                  hover:file:bg-blue-100">
                </div>
                
                <div class="flex justify-end space-x-3 pt-2">
                    <button type="button" onclick="document.getElementById('importModal').classList.add('hidden')" 
                            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12" />
                        </svg>
                        Import
                    </button>
                </div>
            </form>
        </div>
        
        <div class="mt-4 pt-4 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-2">Download Template</h4>
            <a href="{{ asset('templates/students_import_template.xlsx') }}" 
               class="inline-flex items-center text-sm text-blue-600 hover:text-blue-800">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Download Excel Template
            </a>
        </div>
    </div>
</div>

<script>
let currentStudentId = null;

function openModal() {
    currentStudentId = null;
    document.getElementById('modalTitle').textContent = 'Add Student';
    document.getElementById('studentForm').action = "{{ route('students.store') }}";
    document.getElementById('methodField').value = '';
    document.getElementById('studentModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
    clearForm();
}

function openEditModal(id, name, department, program, year, indexNumber) {
    currentStudentId = id;
    document.getElementById('modalTitle').textContent = 'Edit Student';
    document.getElementById('studentForm').action = `/students/${id}`;
    document.getElementById('methodField').value = 'PUT';
    
    // Fill form with existing data
    document.getElementById('name').value = name;
    document.getElementById('department').value = department;
    document.getElementById('program').value = program;
    document.getElementById('year_of_enrollment').value = year;
    document.getElementById('index_number').value = indexNumber;
    
    document.getElementById('studentModal').classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeModal() {
    document.getElementById('studentModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
    clearForm();
}

function clearForm() {
    document.getElementById('studentForm').reset();
    
    // Hide error messages
    document.querySelectorAll('[id^="error-"]').forEach(el => {
        el.classList.add('hidden');
        el.textContent = '';
    });
}

// Close modal when clicking outside
document.getElementById('studentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeModal();
    }
});

// Delete modal functions
let deleteModal = document.getElementById('deleteModal');
let deleteForm = document.getElementById('deleteForm');

function openDeleteModal(studentId, studentName) {
    document.getElementById('studentToDelete').textContent = studentName;
    deleteForm.action = `/students/${studentId}`;
    deleteModal.classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

function closeDeleteModal() {
    deleteModal.classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Close delete modal when clicking outside
deleteModal.addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});

// Auto-submit search form when any input changes
document.querySelectorAll('#searchForm input, #searchForm select').forEach(input => {
    // Don't add change event to the clear filters button
    if (input.type !== 'button') {
        input.addEventListener('change', function() {
            // Add a small delay to allow multiple changes before submitting
            clearTimeout(window.searchTimeout);
            window.searchTimeout = setTimeout(() => {
                document.getElementById('searchForm').submit();
            }, 500);
        });
        
        // For text inputs, also listen to keyup for a more responsive feel
        if (input.type === 'text' || input.type === 'number') {
            input.addEventListener('keyup', function() {
                clearTimeout(window.searchTimeout);
                window.searchTimeout = setTimeout(() => {
                    document.getElementById('searchForm').submit();
                }, 500);
            });
        }
    }
});

// Handle student form submission with AJAX
document.getElementById('studentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const submitButton = this.querySelector('button[type="submit"]');
    const submitButtonText = submitButton.innerHTML;
    
    // Disable submit button and show loading state
    submitButton.disabled = true;
    submitButton.innerHTML = `
        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        Processing...
    `;
    
    // Determine if this is a create or update operation
    const isUpdate = currentStudentId !== null;
    const url = isUpdate ? `/students/${currentStudentId}` : "{{ route('students.store') }}";
    const method = isUpdate ? 'PUT' : 'POST';
    
    // Add _method field for PUT requests
    if (isUpdate) {
        formData.append('_method', 'PUT');
    }
    
    fetch(url, {
        method: 'POST',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Accept': 'application/json'
        },
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(err => {
                throw err;
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            // Close the modal
            closeModal();
            
            // Create a success message element similar to the delete success message
            const successMessage = document.createElement('div');
            successMessage.className = 'mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded';
            successMessage.textContent = data.message;
            
            // Insert the success message after the page title
            const pageTitle = document.querySelector('h2');
            if (pageTitle && pageTitle.nextElementSibling) {
                pageTitle.parentNode.insertBefore(successMessage, pageTitle.nextElementSibling);
            }
            
            // Reload the page to show the updated list after a short delay
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        if (error.errors) {
            // Handle validation errors
            const errorMessages = Object.values(error.errors).flat();
            alert('Error: ' + errorMessages.join('\n'));
        } else {
            alert('An error occurred. Please try again.');
        }
    })
    .finally(() => {
        submitButton.disabled = false;
        submitButton.textContent = originalText;
    });
});
</script>

@endsection