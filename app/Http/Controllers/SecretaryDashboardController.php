<?php

namespace App\Http\Controllers;

use App\Models\IncomingMemo;
use App\Models\OutgoingMemo;
use App\Models\Document;
use App\Models\ActivityLog;
use Illuminate\Support\Facades\Auth;

class SecretaryDashboardController extends Controller
{
    /**
     * Display secretary dashboard metrics.
     */
    public function index()
    {
        $user = Auth::user();

        $incomingCount = IncomingMemo::where('created_by', $user->id)->count();
        $outgoingCount = OutgoingMemo::where('created_by', $user->id)->count();
        $documentCount = Document::where('user_id', $user->id)->count();
        $logCount      = ActivityLog::where('user_id', $user->id)->count();

        return view('dashboard.secretary', compact(
            'incomingCount',
            'outgoingCount',
            'documentCount',
            'logCount'
        ));
    }
}
