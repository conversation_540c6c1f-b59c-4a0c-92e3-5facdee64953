<?php

namespace App\Http\Controllers;

use App\Exports\CgpaTemplateExport;
use Maatwebsite\Excel\Facades\Excel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class CgpaTemplateController extends Controller
{
    /**
     * Download the CGPA import template.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function __invoke(): BinaryFileResponse
    {
        $fileName = 'cgpa_import_template_' . date('Y-m-d') . '.xlsx';
        
        return Excel::download(
            new CgpaTemplateExport(),
            $fileName,
            \Maatwebsite\Excel\Excel::XLSX,
            [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'no-store, no-cache',
                'Pragma' => 'no-cache',
            ]
        );
    }
}
