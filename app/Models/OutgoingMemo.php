<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class OutgoingMemo extends Model
{
    protected $table = 'outgoing_memos';

    protected $fillable = [
        'date_of_dispatch',
        'registry_no',
        'recipient',
        'date_of_letter',
        'letter_no',
        'subject',
        'remark',
        'created_by',
    ];

    protected $casts = [
        'date_of_dispatch' => 'date',
        'date_of_letter'   => 'date',
    ];
}
