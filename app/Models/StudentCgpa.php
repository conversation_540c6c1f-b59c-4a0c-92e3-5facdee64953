<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * Student CGPA model.
 *
 * Represents cumulative GPA records stored in the `student_cgpas` table.
 */
class StudentCgpa extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'student_cgpas';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'index_number',
        'name',
        'gpa',
        'cgpa',
        'totalcredit',
        'totalpoint',
        'program',
        'department',
        'semester',
        'year_of_enrollment',
        'email',
    ];

    /**
     * Scope a query to quickly filter by search term.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  string|null  $term
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, ?string $term)
    {
        if (empty($term)) {
            return $query;
        }

        return $query->where(function ($q) use ($term) {
            $q->where('index_number', 'like', "%{$term}%")
              ->orWhere('name', 'like', "%{$term}%");
        });
    }
}
