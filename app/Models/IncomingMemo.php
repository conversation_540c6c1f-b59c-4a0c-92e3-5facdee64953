<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class IncomingMemo extends Model
{
    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'incoming_memos';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'date_received',
        'registry_no',
        'from_whom_received',
        'date_of_letter',
        'letter_no',
        'subject',
        'remark',
        'created_by',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date_received' => 'date',
        'date_of_letter' => 'date',
    ];
}