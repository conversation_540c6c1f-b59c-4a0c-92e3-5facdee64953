<?php

namespace App\Livewire;

use App\Models\StudentCgpa;
use App\Imports\StudentCgpaImport;
use App\Mail\LowCgpaAlert;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Throwable;

#[\Livewire\Attributes\Layout('layouts.app')]
class CgpaTracker extends Component
{
    use WithFileUploads, WithPagination;

    // Search and filter properties
    public string $search = '';
    public string $filterProgram = '';
    public string $filterSemester = '';
    public bool $belowThresholdOnly = false;
    public float $threshold = 2.0;

    // Modal and state properties
    public ?int $editingId = null;
    public bool $showEditModal = false;
    public bool $showImportModal = false;
    public bool $showExportModal = false;
    public $file;

    // Form data
    public array $form = [
        'index_number' => '',
        'name' => '',
        'gpa' => '0.00',
        'cgpa' => '0.00',
        'totalcredit' => '0',
        'totalpoint' => '0.00',
        'program' => '',
        'department' => '',
        'semester' => '',
        'year_of_enrollment' => '',
        'email' => '',
    ];

    protected $listeners = [
        'delete' => 'delete',
        'show-import-modal' => 'showImportModal',
        'hide-import-modal' => 'hideImportModal',
    ];

    protected function rules(): array
    {
        $rules = [
            'form.index_number' => 'required|string|max:50|unique:student_cgpas,index_number' . ($this->editingId ? ",$this->editingId" : ''),
            'form.name' => 'required|string|max:255',
            'form.gpa' => 'required|numeric|min:0|max:4',
            'form.cgpa' => 'required|numeric|min:0|max:4',
            'form.totalcredit' => 'required|integer|min:0',
            'form.totalpoint' => 'required|numeric|min:0',
            'form.program' => 'required|string|max:255',
            'form.department' => 'required|string|max:255',
            'form.semester' => 'required|string|max:50',
            'form.year_of_enrollment' => 'required|integer|min:1900|max:' . (date('Y') + 5),
            'form.email' => 'nullable|email|max:255',
        ];
        
        return $rules;
    }



    /**
     * Show the import modal and reset related state
     */
    public function showImportModal(): void
    {
        $this->resetErrorBag();
        $this->reset('file');
        $this->showImportModal = true;
    }

    /**
     * Hide the import modal
     */
    public function hideImportModal(): void
    {
        $this->showImportModal = false;
    }

    /**
     * Handle file import
     */
    public function importFile(): void
    {
        try {
            $this->validate([
                'file' => 'required|file|mimes:xlsx,xls,csv|max:10240', // 10MB max
            ]);

            $import = new StudentCgpaImport();
            Excel::import($import, $this->file);
            
            $importedCount = $import->getImportedCount();
            $errorCount = $import->getErrorCount();
            
            $message = "Import completed. {$importedCount} records imported.";
            $alertType = 'success';

            if ($errorCount > 0) {
                $message .= " {$errorCount} records had errors.";
                $alertType = 'warning';
                Log::warning('CGPA Import Errors: ', $import->getValidationFailures());
            }
            
            $this->hideImportModal();
            $this->reset('file');
            $this->resetPage();
            
            $this->dispatch('alert', [
                'type' => $alertType, 
                'message' => $message
            ]);
            
        } catch (Throwable $e) {
            Log::error('Error importing student CGPA: ' . $e->getMessage());
            $this->dispatch('alert', [
                'type' => 'error', 
                'message' => 'Failed to import data: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Component initialization
     */
    public function mount(): void
    {
        // Set default values for the form
        $this->form['year_of_enrollment'] = date('Y');
        $this->form['gpa'] = '0.00';
        $this->form['cgpa'] = '0.00';
        $this->form['totalcredit'] = '0';
        $this->form['totalpoint'] = '0.00';
        
        // You can set a user-specific or global threshold here if needed
        // $this->threshold = config('cgpa.threshold', 2.0);
    }

    /**
     * Render the component
     */
    public function render()
    {
        $query = StudentCgpa::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('index_number', 'like', "%{$this->search}%")
                      ->orWhere('name', 'like', "%{$this->search}%");
                });
            })
            ->when($this->filterProgram, function ($query) {
                $query->where('program', 'like', "%{$this->filterProgram}%");
            })
            ->when($this->filterSemester, function ($query) {
                $query->where('semester', 'like', "%{$this->filterSemester}%");
            })
            ->when($this->belowThresholdOnly, function ($query) {
                $query->where('cgpa', '<', $this->threshold);
            });

        $students = $query->orderBy('cgpa', 'asc')->paginate(10);
        $totalStudents = StudentCgpa::count();
        $atRiskStudents = StudentCgpa::where('cgpa', '<', $this->threshold)->count();

        return view('livewire.cgpa-tracker', [
            'students' => $students,
            'total' => $totalStudents,
            'atRisk' => $atRiskStudents,
            'threshold' => $this->threshold,
        ]);
    }

    /**
     * Prepare for creating a new student
     */
    public function create(): void
    {
        $this->resetForm();
        $this->editingId = null;
        $this->showEditModal = true;
    }

    /**
     * Prepare for editing an existing student
     */
    public function edit(int $id): void
    {
        $student = StudentCgpa::findOrFail($id);
        $this->form = array_merge([
            'gpa' => '0.00',
            'cgpa' => '0.00',
            'totalcredit' => '0',
            'totalpoint' => '0.00',
            'year_of_enrollment' => date('Y'),
        ], $student->toArray());
        
        $this->editingId = $id;
        $this->showEditModal = true;
    }

    /**
     * Reset the form to its default state
     */
    private function resetForm(): void
    {
        $this->form = [
            'index_number' => '',
            'name' => '',
            'gpa' => '0.00',
            'cgpa' => '0.00',
            'totalcredit' => '0',
            'totalpoint' => '0.00',
            'program' => '',
            'department' => '',
            'semester' => '',
            'year_of_enrollment' => date('Y'),
            'email' => ''
        ];
        $this->resetErrorBag();
    }

    /**
     * Save the student record
     */
    public function save(): void
    {
        try {
            // Validate the form data
            $validatedData = $this->validate($this->rules());
            
            DB::beginTransaction();
            
            try {
                $data = $this->form;
                $data['created_by'] = auth()->id();
                
                // Convert string values to appropriate types
                $data['gpa'] = (float)$data['gpa'];
                $data['cgpa'] = (float)$data['cgpa'];
                $data['totalcredit'] = (int)$data['totalcredit'];
                $data['totalpoint'] = (float)$data['totalpoint'];
                $data['year_of_enrollment'] = (int)$data['year_of_enrollment'];
                
                if ($this->editingId) {
                    // Update existing student
                    $student = StudentCgpa::findOrFail($this->editingId);
                    $student->update($data);
                    $message = 'Student CGPA updated successfully.';
                } else {
                    // Create new student
                    StudentCgpa::create($data);
                    $message = 'Student added successfully.';
                }
                
                DB::commit();
                
                $this->showEditModal = false;
                $this->resetForm();
                
                $this->dispatch('alert', [
                    'type' => 'success',
                    'message' => $message
                ]);
                
                // Reset pagination after save
                $this->resetPage();
                
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            $this->setErrorBag($e->validator->getMessageBag());
            Log::error('Validation failed: ' . json_encode($e->errors()));
            
            $this->dispatch('alert', [
                'type' => 'error',
                'message' => 'Please fix the validation errors and try again.'
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Error saving student CGPA: ' . $e->getMessage(), [
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            
            $this->dispatch('alert', [
                'type' => 'error',
                'message' => 'Failed to save student data: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Delete a student record
     * 
     * @param int $id The ID of the student to delete
     * @return void
     */
    public function delete(int $id): void
    {
        try {
            $student = StudentCgpa::findOrFail($id);
            $student->delete();
            
            $this->dispatch('alert', [
                'type' => 'success',
                'message' => 'Student record deleted successfully.'
            ]);
            
        } catch (Throwable $e) {
            Log::error('Error deleting student CGPA: ' . $e->getMessage());
            $this->dispatch('alert', [
                'type' => 'error',
                'message' => 'Failed to delete student record: ' . $e->getMessage()
            ]);
        }
    }

    public function downloadTemplate()
    {
        $path = 'public/templates/students_import_template.xlsx';
        if (!Storage::exists($path)) {
             // Create a dummy file for now if it doesn't exist.
            // In a real scenario, ensure this template is correctly placed.
            $headers = ['index_number', 'name', 'gpa', 'cgpa', 'totalcredit', 'totalpoint', 'program', 'department', 'semester', 'year_of_enrollment', 'email'];
            $data = [$headers]; // Just headers for the template
            $fileName = 'students_import_template.xlsx';
            
            // Temporarily store it in storage/app/public/templates
            Excel::store(new class($data) implements \Maatwebsite\Excel\Concerns\FromArray {
                private $data;
                public function __construct(array $data) { $this->data = $data; }
                public function array(): array { return $this->data; }
            }, 'public/templates/' . $fileName);
            
            if(Storage::exists('public/templates/' . $fileName)){
                 return Storage::download('public/templates/' . $fileName);
            } else {
                 $this->dispatch('error', 'Template file not found and could not be created.');
                 return null;
            }

        }
        return Storage::download($path);
    }

    public function prepareExport()
    {
        $this->showExportModal = true;
    }

    public function performExport()
    {
        try {
            $data = StudentCgpa::query();
            if ($this->search) { $data->search($this->search); }
            if ($this->filterProgram) { $data->where('program', 'like', '%' . $this->filterProgram . '%'); }
            if ($this->filterSemester) { $data->where('semester', 'like', '%' . $this->filterSemester . '%'); }
            if ($this->belowThresholdOnly) { $data->where('cgpa', '<', $this->threshold); }
            
            $studentsToExport = $data->orderBy('cgpa', 'asc')->get();

            if ($studentsToExport->isEmpty()) {
                $this->dispatch('error', 'No data to export based on current filters.');
                $this->hideExportModal();
                return;
            }

            // Using a simple CSV export for now. For Excel, you'd need a dedicated Export class.
            $fileName = 'students_cgpa_export_' . date('YmdHis') . '.csv';
            $headers = [
                'Content-type'        => 'text/csv',
                'Content-Disposition' => "attachment; filename=$fileName",
                'Pragma'              => 'no-cache',
                'Cache-Control'       => 'must-revalidate, post-check=0, pre-check=0',
                'Expires'             => '0'
            ];

            $callback = function() use ($studentsToExport) {
                $file = fopen('php://output', 'w');
                // Add BOM to fix UTF-8 encoding in Excel
                fputs($file, $bom =( chr(0xEF) . chr(0xBB) . chr(0xBF) ));
                
                // Add header row
                fputcsv($file, array_keys($studentsToExport->first()->toArray()));

                foreach ($studentsToExport as $student) {
                    fputcsv($file, $student->toArray());
                }
                fclose($file);
            };
            
            $this->hideExportModal();
            return response()->stream($callback, 200, $headers);

        } catch (Exception $e) {
            Log::error('Error exporting student CGPA data: ' . $e->getMessage());
            $this->dispatch('error', 'Failed to export data. Please check logs.');
            $this->hideExportModal();
        }
    }
    
    public function hideExportModal()
    {
        $this->showExportModal = false;
    }

    public function sendAlert($studentId)
    {
        $student = StudentCgpa::find($studentId);

        try {
            $student = StudentCgpa::findOrFail($studentId);
            
            if (empty($student->email)) {
                throw new Exception('No email address found for this student.');
            }

            if ($student->cgpa >= $this->threshold) {
                throw new Exception('Student is not below the CGPA threshold.');
            }
            
            Mail::to($student->email)->send(new LowCgpaAlert($student, $this->threshold));
            
            $this->dispatch('alert', [
                'type' => 'success',
                'message' => "Alert sent to {$student->name} ({$student->email})."
            ]);
            
        } catch (Exception $e) {
            Log::error('Failed to send alert: ' . $e->getMessage());
            
            $this->dispatch('alert', [
                'type' => 'error',
                'message' => 'Failed to send alert: ' . $e->getMessage()
            ]);
        }
    }
    
    /**
     * Reset pagination when search or filters change
     */
    public function updatedSearch(): void
    {
        $this->resetPage();
    }
    
    public function updatedFilterProgram(): void
    {
        $this->resetPage();
    }
    
    /**
     * Reset pagination when semester filter changes
     */
    public function updatedFilterSemester(): void
    {
        $this->resetPage();
    }
    
    /**
     * Reset pagination when threshold filter changes
     */
    public function updatedBelowThresholdOnly(): void
    {
        $this->resetPage();
    }
}
//
