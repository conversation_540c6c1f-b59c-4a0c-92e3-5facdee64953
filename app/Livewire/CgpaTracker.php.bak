<?php

namespace App\Livewire;

use App\Models\StudentCgpa;
use App\Imports\StudentCgpaImport;
use App\Mail\LowCgpaAlert;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Maatwebsite\Excel\Facades\Excel;
use Exception;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;

#[\Livewire\Attributes\Layout('layouts.app')]
class CgpaTracker extends Component
{
    use WithFileUploads, WithPagination;

    public $search = '';
    public $filterProgram = '';
    public $filterSemester = '';
    public $belowThresholdOnly = false;
    public $threshold = 2.0; // Default threshold

    public $editingId = null;
    public $showEditModal = false;
    public bool $showImportModal = false;

    public $showExportModal = false;
    // public $showImportPathModal = false; // Removed
    // public $importPath = ''; // Removed
    public $file; // For file import

    protected $listeners = [
        'showImportModal' => 'showImportModal',
        'hideImportModal' => 'hideImportModal',
        'delete' => 'delete',
        'show-import-modal' => 'showImportModal'
    ];

    protected function rules()
    {
        // Use the model's rules method
        $rules = [];
        $modelRules = StudentCgpa::rules($this->editingId);
        
        // Map model rules to form field names
        foreach ($modelRules as $field => $rule) {
            $rules["form.{$field}"] = $rule;
        }
        
        return $rules;
    }

    public array $form = [
        'index_number' => '',
        'name' => '',
        'gpa' => '',
        'cgpa' => '',
        'totalcredit' => '',
        'totalpoint' => '',
        'program' => '',
        'department' => '',
        'semester' => '',
        'year_of_enrollment' => '',
        'email' => '',
    ];



    public function showImportModal()
    {
        $this->resetErrorBag();
        $this->reset('file');
        $this->showImportModal = true;
        $this->dispatch('show-import-modal', true);
    }

    public function hideImportModal()
    {
        $this->showImportModal = false;
        $this->dispatch('show-import-modal', false);
    }



    public function importFile()
    {
        try {
            $import = new StudentCgpaImport();
            Excel::import($import, $this->file);
            
            $importedCount = $import->getImportedCount();
            $errorCount = $import->getErrorCount();
            $failures = $import->getValidationFailures();

            $message = "Import completed. {$importedCount} records imported.";
            if ($errorCount > 0) {
                $message .= " {$errorCount} records had errors.";
                Log::warning('CGPA Import Errors: ', $failures);
                $this->dispatch('alert', ['type' => 'warning', 'message' => $message . " Check logs for details."]);
            } else {
                $this->dispatch('alert', ['type' => 'success', 'message' => $message]);
            }
            
            $this->showImportModal = false;
            $this->file = null;
            $this->render();
        } catch (\Exception $e) {
            Log::error('Error importing student CGPA: ' . $e->getMessage());
            $this->dispatch('alert', ['type' => 'error', 'message' => 'Failed to import data: ' . $e->getMessage()]);
        }
    }

    
    public function mount()
    {
        // You can set a user-specific or global threshold here if needed
        // For example, from a config file or user settings
        
        // Initialize form with default values for debugging
        $this->form = [
            'index_number' => '',
            'name' => '',
            'gpa' => '0.00',
            'cgpa' => '0.00',
            'totalcredit' => '0',
            'totalpoint' => '0.00',
            'program' => '',
            'department' => '',
            'semester' => '',
            'year_of_enrollment' => date('Y'),
            'email' => '',
        ];
        
        Log::info('CgpaTracker mounted with initial form data:', $this->form);
    }

    public function render()
    {
        $query = StudentCgpa::query();

        if ($this->search) {
            $query->search($this->search);
        }

        if ($this->filterProgram) {
            $query->where('program', 'like', '%' . $this->filterProgram . '%');
        }

        if ($this->filterSemester) {
            $query->where('semester', 'like', '%' . $this->filterSemester . '%');
        }

        if ($this->belowThresholdOnly) {
            $query->where('cgpa', '<', $this->threshold);
        }

        $students = $query->orderBy('cgpa', 'asc')->paginate(10);

        $totalStudents = StudentCgpa::count();
        $atRiskStudents = StudentCgpa::where('cgpa', '<', $this->threshold)->count();

        return view('livewire.cgpa-tracker', [
            'students' => $students,
            'total' => $totalStudents,
            'atRisk' => $atRiskStudents,
            'threshold' => $this->threshold,
        ]);
    }

    public function create()
    {
        $this->resetForm();
        $this->editingId = null;
        $this->showEditModal = true;
        $this->dispatch('show-edit-modal');
    }

    public function edit($id)
    {
        $student = StudentCgpa::findOrFail($id);
        $this->form = $student->toArray();
        $this->editingId = $id;
        $this->showEditModal = true;
        $this->dispatch('show-edit-modal');
    }

    public function save()
    {
        try {
            // Log the form data before validation
            Log::info('Form data before validation:', $this->form);
            
            // Get validation rules
            $rules = $this->rules();
            Log::info('Validation rules:', $rules);
            
            // Manually validate the data
            $validator = Validator::make($this->form, $rules);
            
            if ($validator->fails()) {
                Log::error('Validation failed:', $validator->errors()->toArray());
                $this->setErrorBag($validator->errors());
                return;
            }
            
            $validatedData = $validator->validated();
            Log::info('Validation passed with data:', $validatedData);
            
            // Prepare the data for saving
            $data = $this->form;
            
            // Add created_by for new records
            if (!$this->editingId) {
                $data['created_by'] = auth()->id();
                Log::info('Adding new student with created_by: ' . $data['created_by']);
            }

            DB::beginTransaction();
            
            try {
                if ($this->editingId) {
                    // Update existing student
                    $student = StudentCgpa::findOrFail($this->editingId);
                    $result = $student->update($data);
                    $message = 'Student CGPA updated successfully.';
                    Log::info('Student update result:', ['success' => $result, 'data' => $data]);
                } else {
                    // Create new student
                    $student = StudentCgpa::create($data);
                    $message = 'Student added successfully.';
                    Log::info('New student created:', $student->toArray());
                }
                
                // Verify the record was saved
                if (isset($student) && $student->exists) {
                    DB::commit();
                    Log::info('Student record verified in database:', $student->toArray());
                    
                    $this->showEditModal = false;
                    $this->dispatch('hide-edit-modal');
                    $this->resetForm();
                    
                    // Show success message
                    session()->flash('message', $message);
                    return redirect()->route('cgpa-tracker');
                } else {
                    throw new \Exception('Failed to verify student record was saved');
                }
            } catch (\Exception $e) {
                DB::rollBack();
                throw $e;
            }
            
        } catch (\Exception $e) {
            $errorMessage = 'Error saving student CGPA: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            Log::error($errorMessage);
            Log::error('Form data at time of error:', $this->form ?? []);
            
            $this->addError('save_error', 'Failed to save student data: ' . $e->getMessage());
            
            // Rethrow the exception to see it in the browser console
            throw $e;
        }
    }

    public function delete($id)
    {
        try {
            StudentCgpa::findOrFail($id)->delete();
            $this->dispatch('saved', 'Student record deleted successfully.');
        } catch (Exception $e) {
            Log::error('Error deleting student CGPA: ' . $e->getMessage());
            $this->dispatch('error', 'Failed to delete student record.');
        }
    }

    public function downloadTemplate()
    {
        $path = 'public/templates/students_import_template.xlsx';
        if (!Storage::exists($path)) {
             // Create a dummy file for now if it doesn't exist.
            // In a real scenario, ensure this template is correctly placed.
            $headers = ['index_number', 'name', 'gpa', 'cgpa', 'totalcredit', 'totalpoint', 'program', 'department', 'semester', 'year_of_enrollment', 'email'];
            $data = [$headers]; // Just headers for the template
            $fileName = 'students_import_template.xlsx';
            
            // Temporarily store it in storage/app/public/templates
            Excel::store(new class($data) implements \Maatwebsite\Excel\Concerns\FromArray {
                private $data;
                public function __construct(array $data) { $this->data = $data; }
                public function array(): array { return $this->data; }
            }, 'public/templates/' . $fileName);
            
            if(Storage::exists('public/templates/' . $fileName)){
                 return Storage::download('public/templates/' . $fileName);
            } else {
                 $this->dispatch('error', 'Template file not found and could not be created.');
                 return null;
            }

        }
        return Storage::download($path);
    }

    public function prepareExport()
    {
        $this->showExportModal = true;
    }

    public function performExport()
    {
        try {
            $data = StudentCgpa::query();
            if ($this->search) { $data->search($this->search); }
            if ($this->filterProgram) { $data->where('program', 'like', '%' . $this->filterProgram . '%'); }
            if ($this->filterSemester) { $data->where('semester', 'like', '%' . $this->filterSemester . '%'); }
            if ($this->belowThresholdOnly) { $data->where('cgpa', '<', $this->threshold); }
            
            $studentsToExport = $data->orderBy('cgpa', 'asc')->get();

            if ($studentsToExport->isEmpty()) {
                $this->dispatch('error', 'No data to export based on current filters.');
                $this->hideExportModal();
                return;
            }

            // Using a simple CSV export for now. For Excel, you'd need a dedicated Export class.
            $fileName = 'students_cgpa_export_' . date('YmdHis') . '.csv';
            $headers = [
                'Content-type'        => 'text/csv',
                'Content-Disposition' => "attachment; filename=$fileName",
                'Pragma'              => 'no-cache',
                'Cache-Control'       => 'must-revalidate, post-check=0, pre-check=0',
                'Expires'             => '0'
            ];

            $callback = function() use ($studentsToExport) {
                $file = fopen('php://output', 'w');
                // Add BOM to fix UTF-8 encoding in Excel
                fputs($file, $bom =( chr(0xEF) . chr(0xBB) . chr(0xBF) ));
                
                // Add header row
                fputcsv($file, array_keys($studentsToExport->first()->toArray()));

                foreach ($studentsToExport as $student) {
                    fputcsv($file, $student->toArray());
                }
                fclose($file);
            };
            
            $this->hideExportModal();
            return response()->stream($callback, 200, $headers);

        } catch (Exception $e) {
            Log::error('Error exporting student CGPA data: ' . $e->getMessage());
            $this->dispatch('error', 'Failed to export data. Please check logs.');
            $this->hideExportModal();
        }
    }
    
    public function hideExportModal()
    {
        $this->showExportModal = false;
    }

    public function sendAlert($studentId)
    {
        $student = StudentCgpa::find($studentId);

        if (!$student) {
            $this->dispatch('error', 'Student not found.');
            return;
        }

        if (!$student->email) {
            $this->dispatch('error', "Student {$student->name} does not have an email address on record.");
            return;
        }

        if ($student->cgpa >= $this->threshold) {
            $this->dispatch('error', "Student {$student->name} is not below the CGPA threshold.");
            return;
        }
        
        try {
            // Assuming User model and Mail/LowCgpaAlert exists and is configured
            $user = Auth::user(); // Or however you get the sender's info
            Mail::to($student->email)->send(new LowCgpaAlert($student, $user));
            $this->dispatch('saved', "Alert sent to {$student->name} ({$student->email}).");
        } catch (Exception $e) {
            Log::error("Failed to send CGPA alert to {$student->email}: " . $e->getMessage());
            $this->dispatch('error', 'Failed to send alert. Please check system logs.');
        }
    }

    private function resetForm()
    {
        $this->form = [
            'index_number' => '',
            'name' => '',
            'gpa' => '',
            'cgpa' => '',
            'totalcredit' => '',
            'totalpoint' => '',
            'program' => '',
            'department' => '',
            'semester' => '',
            'year_of_enrollment' => '',
            'email' => '',
        ];
        $this->resetErrorBag();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }
    public function updatedFilterProgram()
    {
        $this->resetPage();
    }
    public function updatedFilterSemester()
    {
        $this->resetPage();
    }
     public function updatedBelowThresholdOnly()
    {
        $this->resetPage();
    }
}
//
