<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\OutgoingMemo;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;

#[\Livewire\Attributes\Layout('layouts.app')]
class OutgoingMemoComponent extends Component
{
    use WithPagination;

    // Form fields
    public $date_of_dispatch, $registry_no, $recipient, $date_of_letter, $letter_no, $subject, $remark;

    // Component state
    public $showModal = false;
    public $editingId = null;
    public $search = '';
    public $filters = [
        'date_from' => '',
        'date_to' => '',
        'recipient' => ''
    ];

    protected $rules = [
        'date_of_dispatch' => 'required|date',
        'registry_no'      => 'required|integer|max:20|unique:outgoing_memos,registry_no',
        'recipient'        => 'required|string|max:100',
        'date_of_letter'   => 'required|date|before_or_equal:date_of_dispatch',
        'letter_no'        => 'required|integer|max:20',
        'subject'          => 'required|string|min:100',
        'remark'           => 'nullable|string|max:100',
    ];

    protected $listeners = ['refreshComponent' => '$refresh'];

    public function resetForm()
    {
        $this->reset([
            'date_of_dispatch', 'registry_no', 'recipient',
            'date_of_letter', 'letter_no', 'subject', 'remark',
            'editingId'
        ]);
        $this->resetValidation();
    }

    public function create()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function resetFilters()
    {
        $this->reset('filters', 'search');
        $this->resetPage(); // Reset pagination when filters are reset
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function edit($id)
    {
        $memo = OutgoingMemo::findOrFail($id);
        $this->editingId = $id;
        $this->date_of_dispatch = optional($memo->date_of_dispatch)->format('Y-m-d');
        $this->registry_no = $memo->registry_no;
        $this->recipient = $memo->recipient;
        $this->date_of_letter = optional($memo->date_of_letter)->format('Y-m-d');
        $this->letter_no = $memo->letter_no;
        $this->subject = $memo->subject;
        $this->remark = $memo->remark;
        $this->showModal = true;
    }

    public function save()
    {
        try {
            $rules = $this->rules;
            if ($this->editingId) {
                $rules['registry_no'] = 'required|integer|max:20|unique:outgoing_memos,registry_no,' . $this->editingId;
            }
            $validated = $this->validate($rules);

            if (empty($validated['date_of_dispatch'])) {
                flash()->error('Date of dispatch is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    Validator::make([], []),
                    ['date_of_dispatch' => ['Date of dispatch is required.']],
                    'date_of_dispatch'
                );
            }

            if (empty($validated['registry_no'])) {
                flash()->error('Registry number is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    Validator::make([], []),
                    ['registry_no' => ['Registry number is required.']],
                    'registry_no'
                );
            }

            if (empty($validated['recipient'])) {
                flash()->error('Recipient is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    Validator::make([], []),
                    ['recipient' => ['Recipient is required.']],
                    'recipient'
                );
            }

            if (empty($validated['date_of_letter'])) {
                flash()->error('Date of letter is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    Validator::make([], []),
                    ['date_of_letter' => ['Date of letter is required.']],
                    'date_of_letter'
                );
            }

            if (empty($validated['letter_no'])) {
                flash()->error('Letter number is required.', ['title' => 'Error']);
                throw \Illuminate\Validation\ValidationException::withMessages([
                    'letter_no' => ['Letter number is required.'],
                ]);
            }

            if (empty($validated['subject'])) {
                flash()->error('Subject is required.', ['title' => 'Error']);
                throw \Illuminate\Validation\ValidationException::withMessages([
                    'subject' => ['Subject is required.'],
                ]);
            }

            $data = [
                'date_of_dispatch' => $validated['date_of_dispatch'],
                'registry_no'      => $validated['registry_no'],
                'recipient'        => $validated['recipient'],
                'date_of_letter'   => $validated['date_of_letter'],
                'letter_no'        => $validated['letter_no'],
                'subject'          => $validated['subject'],
                'remark'           => $validated['remark'] ?? null,
                'created_by'       => Auth::id(),
            ];

            if ($this->editingId) {
                OutgoingMemo::findOrFail($this->editingId)->update($data);
                flash()->success('Memo updated successfully.', ['title' => 'Success']);
            } else {
                OutgoingMemo::create($data);
                flash()->success('Memo created successfully.', ['title' => 'Success']);
            }

            $this->closeModal();
            $this->resetPage();
        } catch (\Illuminate\Validation\ValidationException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('Outgoing memo save error: ' . $e->getMessage());
            flash()->error('An error occurred while saving the memo. Please try again.', ['title' => 'Error']);
        }
    }

    public function delete($id)
    {
        try {
            OutgoingMemo::findOrFail($id)->delete();
            flash()->success('Memo deleted successfully.', ['title' => 'Success']);
            $this->resetPage();
        } catch (\Exception $e) {
            Log::error('Outgoing memo delete error: ' . $e->getMessage());
            flash()->error('Error deleting memo. Please try again.', ['title' => 'Error']);
        }
    }

    // For search / filters reactive updates
    public function updatedSearch() { $this->resetPage(); }
    public function updatedFilters() { $this->resetPage(); }

    public function render()
    {
        $memos = OutgoingMemo::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('registry_no', 'like', '%' . $this->search . '%')
                      ->orWhere('recipient', 'like', '%' . $this->search . '%')
                      ->orWhere('subject', 'like', '%' . $this->search . '%');
                });
            })
            ->when($this->filters['date_from'], fn ($q) => $q->where('date_sent', '>=', $this->filters['date_from']))
            ->when($this->filters['date_to'], fn ($q) => $q->where('date_sent', '<=', $this->filters['date_to']))
            ->when($this->filters['recipient'], fn ($q) => $q->where('recipient', 'like', '%' . $this->filters['recipient'] . '%'))
            ->orderBy('date_of_dispatch', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('livewire.outgoing-memo', [
            'memos' => $memos,
        ]);
    }
}
