<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;

class CgpaTemplateExport implements FromCollection, WithHeadings, WithTitle
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        // Return sample data for the template
        return collect([
            [
                'index_number' => '12345678',
                'name' => '<PERSON>',
                'gpa' => '3.5',
                'cgpa' => '3.2',
                'totalcredit' => '15',
                'totalpoint' => '48',
                'program' => 'Computer Science',
                'department' => 'Computing',
                'semester' => 'First',
                'year_of_enrollment' => '2023',
                'email' => '<EMAIL>'
            ],
            [
                'index_number' => '23456789',
                'name' => '<PERSON>',
                'gpa' => '3.8',
                'cgpa' => '3.6',
                'totalcredit' => '15',
                'totalpoint' => '54',
                'program' => 'Information Technology',
                'department' => 'Computing',
                'semester' => 'Second',
                'year_of_enrollment' => '2023',
                'email' => '<EMAIL>'
            ]
        ]);
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'index_number',
            'name',
            'gpa',
            'cgpa',
            'totalcredit',
            'totalpoint',
            'program',
            'department',
            'semester',
            'year_of_enrollment',
            'email'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'CGPA Template';
    }
}
